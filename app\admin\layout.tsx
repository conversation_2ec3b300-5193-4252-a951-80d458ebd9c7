"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { Sidebar } from "@/components/admin/sidebar"
import { useAuth } from "@/contexts/auth-context"

export default function AdminLayout({ children }: { children: React.ReactNode }) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [isAuthed, setIsAuthed] = useState(false)
    const { user } = useAuth()

    useEffect(() => {
        // Check for admin cookie client-side
        const cookies = document.cookie.split(';').map(c => c.trim())
        const adminCookie = cookies.find(c => c.startsWith('adminAuthenticated='))
        const isAdminAuthenticated = !!adminCookie && adminCookie.split('=')[1] === 'true'

        // Also check localStorage for backward compatibility
        const localStorageAuth = localStorage.getItem("adminAuthenticated") === "true"

        // If either authentication method is valid, allow access
        setIsAuthed(isAdminAuthenticated || localStorageAuth)

        // If not authenticated, redirect to admin login
        if (!isAdminAuthenticated && !localStorageAuth) {
            toast.error("Admin authentication required")
            router.replace('/admin-login')
        }

        setIsLoading(false)
    }, [router])

    if (isLoading) return null
    if (!isAuthed) return null

    return (
        <div className="flex min-h-screen bg-gray-50">
            <Sidebar />
            <div className="flex-1">
                {children}
            </div>
        </div>
    )
} 
