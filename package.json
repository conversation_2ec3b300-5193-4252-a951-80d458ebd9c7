{"name": "modrivepro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.6", "@radix-ui/react-checkbox": "^1.0.6", "@radix-ui/react-dialog": "^1.0.6", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.6", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-radio-group": "^1.0.6", "@radix-ui/react-scroll-area": "^1.0.6", "@radix-ui/react-select": "^2.0.6", "@radix-ui/react-separator": "^1.0.6", "@radix-ui/react-slider": "^1.0.6", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.6", "@radix-ui/react-tabs": "^1.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "keen-slider": "^6.8.6", "lucide-react": "^0.263.1", "next": "^14.2.28", "react": "^18.2.0", "react-calendar": "^6.0.0", "react-datepicker": "^8.4.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.74"}, "devDependencies": {"@eslint/eslintrc": "^2.1.4", "@types/node": "^20.11.30", "@types/react": "^18.2.62", "@types/react-calendar": "^4.1.0", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.4.1", "typescript": "^5.4.5"}}