import type React from "react"
import type { Metada<PERSON> } from "next"
import { Mulish } from "next/font/google"
import "./globals.css"
import "./rtl.css"
import { Toaster } from "sonner"
import { AuthProvider } from "@/contexts/auth-context"
import { MessagesProvider } from "@/contexts/messages-context"
import { MainNavbar } from "@/components/layout/main-navbar"
import { SiteFooter } from "@/components/layout/site-footer"
import { I18nProvider } from "@/i18n/i18n-provider"
import ClientLayoutWrapper from "@/components/ClientLayoutWrapper"
import { PaymentRequestsProvider } from "@/contexts/payment-requests-context"
import { usePathname } from "next/navigation"

const mulish = Mulish({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-mulish",
})

export const metadata: Metadata = {
  title: "morentcar - Car Rental in Morocco",
  description: "Discover Morocco on wheels with morentcar car rental service",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
  const isAdminRoute = pathname.startsWith('/admin');
  return (
    <html lang="en">
      <body className={mulish.variable}>
        <I18nProvider>
          <MessagesProvider>
            <PaymentRequestsProvider>
              <ClientLayoutWrapper>
                {isAdminRoute ? (
                  children
                ) : (
                  <>
                    <MainNavbar />
                    {children}
                    <SiteFooter />
                  </>
                )}
              </ClientLayoutWrapper>
            </PaymentRequestsProvider>
          </MessagesProvider>
        </I18nProvider>
      </body>
    </html>
  )
}
